# All Game Modes

#normie
[[enum]]
id=10012
keytype="int"
valuetype="string"
default=""

[enum.values]
0="Regular - 300x Combat, 50x Skilling, No drop rate bonus"
1="Challenge - 80x Combat, 25x Skilling, <col=00ff00>5%</col> drop rate bonus"
2="Realism - 10x Combat, 5x Skilling, <col=00ff00>13%</col> drop rate bonus"

#ironman
[[enum]]
id=10013
keytype="int"
valuetype="string"
default=""

[enum.values]
0="Regular - 300x Combat, 50x Skilling, No drop rate bonus"
1="Challenge - 80x Combat, 25x Skilling, <col=00ff00>5%</col> drop rate bonus"
2="Realism - 10x Combat, 5x Skilling, <col=00ff00>13%</col> drop rate bonus"

#hcim
[[enum]]
id=10014
keytype="int"
valuetype="string"
default=""

[enum.values]
0="Regular - 300x Combat, 50x Skilling, No drop rate bonus"
1="Challenge - 80x Combat, 25x Skilling, <col=00ff00>5%</col> drop rate bonus"
2="Realism - 10x Combat, 5x Skilling, <col=00ff00>13%</col> drop rate bonus"

#uim
[[enum]]
id=10015
keytype="int"
valuetype="string"
default=""

[enum.values]
0="Regular - 300x Combat, 50x Skilling, No drop rate bonus"
1="Challenge - 80x Combat, 25x Skilling, <col=00ff00>5%</col> drop rate bonus"
2="Realism - 10x Combat, 5x Skilling, <col=00ff00>13%</col> drop rate bonus"

#gim
[[enum]]
id=10016
keytype="int"
valuetype="string"
default=""

[enum.values]
0="Regular - 300x Combat, 50x Skilling, No drop rate bonus"
1="Challenge - 80x Combat, 25x Skilling, <col=00ff00>5%</col> drop rate bonus"
2="Realism - 10x Combat, 5x Skilling, <col=00ff00>13%</col> drop rate bonus"

