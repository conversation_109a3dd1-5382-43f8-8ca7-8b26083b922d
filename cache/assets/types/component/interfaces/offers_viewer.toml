id=1709
name="offers_viewer"

# Universe
[[component]]
name="universe"
type="layer"
widthmode=1
heightmode=1

# Layout
[[component]]
name="layout"
type="layer"
layer="universe"
widthmode=1
heightmode=1
xmode=1
ymode=1

# Main container
[[component]]
name="main_container"
type="layer"
layer="universe"
widthmode=1
heightmode=1
onload=[10800]

# Bottom bar seperator
[[component]]
type="graphic"
layer="main_container"
sprite=314
spritetiling=true
x=5
y=40
width=502
height=36
ymode=2
onclick=[10117]

# Exchange button
[[component]]
type="layer"
layer="main_container"
op1="Exchange"
clickmask=2
x=7
y=5
width=57
height=20
onload=[540, "component:self", "Exchange"]

# Search item button
[[component]]
name="search_item"
type="layer"
layer="main_container"
op1="Search for item"
clickmask=2
x=14
y=13
ymode=2
width=40
height=36

# Search sprite 1
[[component]]
type="graphic"
layer="search_item"
sprite=1120
widthmode=1
heightmode=1

# Search sprite 2
[[component]]
type="graphic"
layer="search_item"
sprite=1121
widthmode=1
heightmode=1
ontimer=[811, "component:self", -1, 0, 100, 250]

# Item placeholder
[[component]]
type="graphic"
layer="search_item"
bordertype=1
shadowcolor="333333"
x=2
y=2
width=36
height=32

# Magnifying glass
[[component]]
type="graphic"
layer="search_item"
sprite=1113
width=20
height=18

# Item information container
[[component]]
name="item_information"
type="layer"
layer="main_container"
x=54
y=8
ymode=2
width=448
height=45

# Item name
[[component]]
type="text"
layer="item_information"
font=496
xallignment=1
yallignment=1
y=5
width=140
height=35

# Seperator
[[component]]
type="line"
layer="item_information"
linewidth=45
color="ff981f"
x=144
y=22
width=1

# Grand Exchange icon
[[component]]
type="graphic"
layer="item_information"
sprite=1112
x=160
y=3
width=25
height=23

[[component]]
type="text"
layer="item_information"
text="GE"
font=495
x=165
y=27
width=20
height=20

# Item price
[[component]]
type="text"
layer="item_information"
font=495
xallignment=1
yallignment=1
x=186
y=6
width=95
height=35

# Buying button
[[component]]
type="graphic"
layer="item_information"
sprite=699
op1="Select"
clickmask=2
x=382
y=3
width=17
height=17
onclick=[10806, 1]

# Selling button
[[component]]
type="graphic"
layer="item_information"
sprite=697
op1="Select"
clickmask=2
x=382
y=24
width=17
height=17
onclick=[10806, 0]

# Buying text
[[component]]
type="text"
layer="item_information"
text="Buying"
font=495
op1="Select"
clickmask=2
x=405
y=4
width=50
height=20
onclick=[10806, 1]

# Selling text
[[component]]
type="text"
layer="item_information"
text="Selling"
font=495
op1="Select"
clickmask=2
x=405
y=25
width=50
height=20
onclick=[10806, 0]

# Offer results
[[component]]
name="offer_results"
type="layer"
layer="main_container"
hidden=true
x=8
y=36
width=494
height=103
heightmode=1

# Black outline
[[component]]
type="rectangle"
layer="offer_results"
color="000000"
opacity=100
widthmode=1
heightmode=1

# Orange outline
[[component]]
type="rectangle"
layer="offer_results"
color="ff981f"
opacity=200
x=1
y=1
widthmode=1
heightmode=1
width=2
height=2

# Offer results background
[[component]]
type="rectangle"
layer="offer_results"
color="4e453a"
filled=true
x=2
y=2
widthmode=1
heightmode=1
width=4
height=4

# Black outline for column row
[[component]]
type="rectangle"
layer="offer_results"
color="000000"
opacity=100
widthmode=1
height=22

# Orange outline for column row
[[component]]
type="rectangle"
layer="offer_results"
color="ff981f"
opacity=200
x=1
y=1
widthmode=1
width=2
height=20

# Quantity column
[[component]]
type="text"
layer="offer_results"
text="Quantity"
font=494
color="ffffff"
xallignment=1
yallignment=1
x=2
y=1
width=50
height=20

# Price column
[[component]]
type="text"
layer="offer_results"
text="Price"
font=494
color="ffffff"
xallignment=1
yallignment=1
x=96
y=1
width=50
height=20

# Seller column
[[component]]
type="text"
layer="offer_results"
text="Seller"
font=494
color="ffffff"
xallignment=1
yallignment=1
x=210
y=1
width=50
height=20

# Quantity sorter
[[component]]
type="layer"
layer="offer_results"
op1="Sort by quantity"
clickmask=2
x=49
y=9
width=11
height=4
onload=[10807, "component:self", 0, 1, "Sort by quantity"]

# Price sorter
[[component]]
type="layer"
layer="offer_results"
op1="Sort by price"
clickmask=2
x=135
y=9
width=11
height=4
onload=[10807, "component:self", 2, 3, "Sort by price"]

# Name sorter
[[component]]
type="layer"
layer="offer_results"
op1="Sort by name"
clickmask=2
x=251
y=9
width=11
height=4
onload=[10807, "component:self", 4, 5, "Sort by name"]

# Results scroll layer
[[component]]
name="results_scroll_layer"
type="layer"
layer="offer_results"
x=1
y=23
width=493
height=25
heightmode=1

# Results scroll bar
[[component]]
type="layer"
layer="offer_results"
x=2
y=23
width=16
height=25
heightmode=1
xmode=2

# Instructions text
[[component]]
type="text"
layer="main_container"
text="Search for an item and what type of offers you're looking for."
color="ffffff"
font=495
xallignment=1
yallignment=1
x=8
y=36
width=469
height=101
heightmode=1

# Tooltip
[[component]]
name="tooltip"
type="layer"
layer="universe"
width=1
height=1