// 10585
[clientscript,script10585](component $component0, int $int1, component $component2, component $component3, component $component4, int $int5)
if ($int5 = 0 & calc(clientclock % 50) ! 0) {
	return;
}
def_int $int6 = %var3800;
def_int $int7 = calc($int6 / 86400);
def_int $int8 = calc($int6 / 3600 % 24);
def_int $int9 = calc($int6 / 60 % 60);
def_int $int10 = calc($int6 % 60);
def_int $int11 = %var3805;
def_int $int12 = calc($int11 / 86400);
def_int $int13 = calc($int11 / 3600 % 24);
def_int $int14 = calc($int11 / 60 % 60);
def_int $int15 = calc($int11 % 60);
def_int $int16 = %var3806;
def_int $int17 = calc($int16 / 86400);
def_int $int18 = calc($int16 / 3600 % 24);
def_int $int19 = calc($int16 / 60 % 60);
def_int $int20 = calc($int16 % 60);
def_int $int21 = %var3801;
def_int $int22 = calc($int21 / 86400);
def_int $int23 = calc($int21 / 3600 % 24);
def_int $int24 = calc($int21 / 60 % 60);
def_int $int25 = calc($int21 % 60);
%var3800 = calc(%var3800 + 1);
%var3802 = calc(%var3802 + 1);
if (%var3805 > 0) {
	%var3805 = calc(%var3805 - 1);
	if_settext("Global BXP: <col=00ff00><tostring($int12)>d <tostring($int13)>h <tostring($int14)>m <tostring($int15)>s</col>", $component3);
} else {
	if_settext("Global BXP: <col=ff0000>Inactive</col>", $component3);
}
if (%var3806 > 0) {
	%var3806 = calc(%var3806 - 1);
	if_settext("CoX Boost: <col=00ff00><tostring($int17)>d <tostring($int18)>h <tostring($int19)>m <tostring($int20)>s</col>", $component4);
} else {
	if_settext("CoX Boost: <col=ff0000>Inactive</col>", $component4);
}
if (%var3801 > 0) {
	%var3801 = calc(%var3801 - 1);
	if_settext("Private BXP: <col=00ff00><tostring($int22)>d <tostring($int23)>h <tostring($int24)>m <tostring($int25)>s</col>", $component2);
} else if (%var3805 > 0) {
	if_settext("Private BXP: <col=ffffff><tostring($int22)>d <tostring($int23)>h <tostring($int24)>m <tostring($int25)>s</col>", $component2);
} else {
	if_settext("Private BXP: <col=ff0000>Inactive</col>", $component2);
}
if_settext("Up-time: <col=ffffff><tostring($int7)>d <tostring($int8)>h <tostring($int9)>m <tostring($int10)>s</col>", $component0);
