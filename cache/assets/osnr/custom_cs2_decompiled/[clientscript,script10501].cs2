// 10501
[clientscript,script10501](string $string0)
def_string $string1 = "";
def_string $string2 = "";
def_int $int0 = 0;
def_int $int1 = 0;
def_int $int2 = 0;
def_int $int3 = 0;
def_component $component4 = 100guide_eggs_overlay:0;
def_int $int5 = 0;
def_int $int6 = 0;
def_string $string3 = "";
def_string $string4 = "";
def_string $string5 = "";
while (string_length($string0) > 0) {
	$string1, $string0 = ~script632($string0);
	$string2 = $string1;
	$string1, $string0 = ~script632($string0);
	$int1 = ~script10565($string1);
	$string1, $string0 = ~script632($string0);
	$int2 = ~script10565($string1);
	$string1, $string0 = ~script632($string0);
	$int3 = ~script10565($string1);
	$string1, $string0 = ~script632($string0);
	$int5 = ~script10565($string1);
	$string1, $string0 = ~script632($string0);
	$int6 = ~script10565($string1);
	$string1, $string0 = ~script632($string0);
	$string3 = $string1;
	$string1, $string0 = ~script632($string0);
	$string4 = $string1;
	$string1, $string0 = ~script632($string0);
	$string5 = $string1;
	$component4 = ~script10503($int0);
	if_setposition(0, calc($int0 * 102), ^setpos_abs_left, ^setpos_abs_top, $component4);
	~script10502($component4, $string2, $int1, $int2, $int3, $int5, $int6, $string3, $string4, $string5);
	$int0 = calc($int0 + 1);
}
def_int $height7 = calc($int0 * 102);
if_setscrollpos(0, 0, interface_1600:8);
~scrollbar_vertical(104857609, 104857608, 792, 789, 790, 791, 773, 788);
if ($height7 > if_getheight(interface_1600:8)) {
	if_setscrollsize(0, $height7, interface_1600:8);
} else {
	if_setscrollsize(0, 0, interface_1600:8);
}
~scrollbar_resize(104857609, 104857608, 0);
