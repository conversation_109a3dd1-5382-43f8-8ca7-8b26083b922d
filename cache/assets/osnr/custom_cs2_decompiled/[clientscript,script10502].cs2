// 10502
[clientscript,script10502](component $component0, graphic $graphic1, int $int2, int $height3, int $int4, int $int5, string $string0, string $string1, string $string2, string $string3)
def_int $int6 = 0;
def_int $int7 = calc($int5 / 2);
def_int $int8 = 0;
def_int $int9 = 0;
if ($int5 > 0) {
	if ($int4 <= $int7) {
		$int8 = 150;
		$int9 = interpolate(0, $int4, 0, $int7, 150);
	} else {
		$int8 = calc(150 - interpolate(0, $int4 - $int7, 0, $int7, 150));
		$int9 = 150;
	}
}
def_int $colour10 = ~rgb_to_hex($int8, $int9, 0);
cc_create($component0, ^iftype_rectangle, $int6);
$int6 = calc($int6 + 1);
cc_setsize(0, 0, ^setsize_minus, ^setsize_minus);
cc_setposition(0, 0, ^setpos_abs_centre, ^setpos_abs_centre);
cc_setcolour($colour10);
cc_setfill(true);
cc_settrans(225);
cc_setonmouseover("cc_settrans(event_com, event_comsubid, 180, -1)");
cc_setonmouseleave("cc_settrans(event_com, event_comsubid, 225, -1)");
cc_setop(1, "Claim");
cc_setopbase("<col=ff981f><$string0>");
$int6 = ~stonepanel(-1, $component0, $int6);
def_int $width11 = parawidth($string0, 512, p12_full);
cc_create($component0, ^iftype_text, $int6);
$int6 = calc($int6 + 1);
cc_setsize($width11, $height3, ^setsize_abs, ^setsize_abs);
cc_setposition(calc((if_getwidth($component0) - $width11) / 2), 6, ^setpos_abs_left, ^setpos_abs_top);
cc_setcolour(0xff981f);
cc_settextshadow(true);
cc_settextfont(p12_full);
cc_settextalign(^settextalign_centre, ^settextalign_centre, 0);
cc_settext($string0);
.cc_create($component0, ^iftype_graphic, $int6);
$int6 = calc($int6 + 1);
.cc_setsize($int2, $height3, ^setsize_abs, ^setsize_abs);
.cc_setposition(calc(cc_getx - $int2 - 1), 6, ^setpos_abs_left, ^setpos_abs_top);
.cc_setgraphic($graphic1);
cc_create($component0, ^iftype_rectangle, $int6);
$int6 = calc($int6 + 1);
cc_setsize(6, 21, ^setsize_minus, ^setsize_abs);
cc_setposition(0, 4, ^setpos_abs_centre, ^setpos_abs_bottom);
cc_setcolour(0x0e0e0c);
cc_create($component0, ^iftype_rectangle, $int6);
$int6 = calc($int6 + 1);
cc_setsize(8, 19, ^setsize_minus, ^setsize_abs);
cc_setposition(0, 5, ^setpos_abs_centre, ^setpos_abs_bottom);
cc_setcolour(0x474745);
cc_create($component0, ^iftype_graphic, $int6);
$int6 = calc($int6 + 1);
cc_setsize(10, 17, ^setsize_minus, ^setsize_abs);
cc_setposition(0, 6, ^setpos_abs_centre, ^setpos_abs_bottom);
cc_settiling(true);
cc_setgraphic("tradebacking_dark");
def_int $int12 = calc(if_getwidth($component0) - 10);
cc_create($component0, ^iftype_rectangle, $int6);
$int6 = calc($int6 + 1);
cc_setsize(scale($int4, $int5, $int12), 17, ^setsize_abs, ^setsize_abs);
cc_setposition(5, 6, ^setpos_abs_left, ^setpos_abs_bottom);
cc_setcolour($colour10);
cc_setfill(true);
cc_create($component0, ^iftype_text, $int6);
$int6 = calc($int6 + 1);
cc_setsize(10, 15, ^setsize_minus, ^setsize_abs);
cc_setposition(0, 6, ^setpos_abs_centre, ^setpos_abs_bottom);
cc_setcolour(^white);
cc_settextshadow(true);
cc_settextfont(p11_full);
cc_settextalign(^settextalign_centre, ^settextalign_centre, 0);
cc_settext("<tostring($int4)> / <tostring($int5)>");
cc_create($component0, ^iftype_text, $int6);
$int6 = calc($int6 + 1);
cc_setsize(6553, 20, ^setsize_2, ^setsize_abs);
cc_setposition(0, 32, ^setpos_abs_left, ^setpos_abs_top);
cc_setcolour(0xff981f);
cc_settextshadow(true);
cc_settextfont(b12_full);
cc_settextalign(^settextalign_centre, ^settextalign_centre, 0);
cc_settext("Category: <col=ffffff><$string1>");
cc_create($component0, ^iftype_text, $int6);
$int6 = calc($int6 + 1);
cc_setsize(6553, 20, ^setsize_2, ^setsize_abs);
cc_setposition(0, 32, ^setpos_abs_right, ^setpos_abs_top);
cc_setcolour(0xff981f);
cc_settextshadow(true);
cc_settextfont(b12_full);
cc_settextalign(^settextalign_centre, ^settextalign_centre, 0);
cc_settext("Difficulty: <col=ffffff><$string2>");
cc_create($component0, ^iftype_text, $int6);
$int6 = calc($int6 + 1);
cc_setsize(6, 20, ^setsize_minus, ^setsize_abs);
cc_setposition(0, 55, ^setpos_abs_right, ^setpos_abs_top);
cc_setcolour(0xff981f);
cc_settextshadow(true);
cc_settextfont(b12_full);
cc_settextalign(^settextalign_centre, ^settextalign_centre, 0);
cc_settext("Reward: <col=ffffff><$string3>");
