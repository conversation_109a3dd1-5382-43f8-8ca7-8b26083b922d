// 32076
[proc,script32076](component $component0, int $int1, string $op0, string $string1, fontmetrics $fontmetrics2, int $colour3)(int)
cc_deleteall($component0);
def_int $int4 = 1;
if (string_length($string1) = 0) {
	$int4 = 0;
}
if ($int4 = 1) {
	$int1 = ~v2_stone_button_filled($component0);
	if_setonmouseover("v2_stone_button_change_in($component0, 0)", $component0);
	if_setonmouseleave("v2_stone_button_change_out($component0, 0)", $component0);
} else {
	$int1 = ~v2_stone_button_in_filled($component0);
	if_setonmouseover(null, $component0);
	if_setonmouseleave(null, $component0);
}
cc_create($component0, ^iftype_text, $int1);
$int1 = calc($int1 + 1);
cc_setposition(0, 0, ^setpos_abs_left, ^setpos_abs_top);
cc_setsize(0, 0, ^setsize_minus, ^setsize_minus);
cc_settextfont($fontmetrics2);
cc_settextshadow(true);
cc_setcolour($colour3);
cc_settextalign(^settextalign_centre, ^settextalign_centre, 12);
cc_settext($op0);
cc_setop(1, $op0);
cc_setopbase("<col=ff981f><$string1></col>");
return($int1);
