// 32010
[proc,script32010](int $int0, int $int1, int $int2, int $int3, int $int4, int $comsubid5, component $component6, string $string0)(int, int)
def_int $width7 = calc($int3 - 12);
def_int $int8 = calc(paraheight($string0, $width7, p12_full) * 12 + 5);
def_int $int9 = calc(paraheight($string0, $width7, p12_full) * 12 + 5);
def_int $int10 = calc(6 + $int8 + 3 + 21 + 3 + $int9 + 6);
def_int $int11 = -1;
def_int $int12 = -1;
def_int $int13 = -1;
def_int $int14 = -1;
if (.cc_find($component6, $comsubid5) = ^true) {
	.cc_setsize($int3, $int10, ^setsize_abs, ^setsize_abs);
	.cc_setposition($int1, $int2, ^setpos_abs_left, ^setpos_abs_top);
	.cc_setop(1, $string0);
	cc_create($component6, ^iftype_rectangle, $int0);
	$int11 = $int0;
	$int0 = calc($int0 + 1);
	cc_setsize($int3, $int10, ^setsize_abs, ^setsize_abs);
	cc_setposition($int1, $int2, ^setpos_abs_left, ^setpos_abs_top);
	cc_setcolour(^white);
	cc_setfill(true);
	cc_settrans(228);
	cc_create($component6, ^iftype_text, $int0);
	$int0 = calc($int0 + 1);
	cc_setsize($width7, $int8, ^setsize_abs, ^setsize_abs);
	cc_setposition(calc($int1 + 6), calc($int2 + 6), ^setpos_abs_left, ^setpos_abs_top);
	cc_setcolour(0xff981f);
	cc_settextshadow(true);
	cc_settextfont(verdana_15pt_regular);
	cc_settextalign(^settextalign_centre, ^settextalign_centre, 0);
	cc_settext($string0);
	cc_create($component6, ^iftype_graphic, $int0);
	$int14 = $int0;
	$int0 = calc($int0 + 1);
	cc_setsize(13, 13, ^setsize_abs, ^setsize_abs);
	cc_setposition(calc($int1 + ($int3 - $int4) / 2 + 6), calc($int2 + 6 + $int8 + 3 + 4), ^setpos_abs_left, ^setpos_abs_top);
}
return($int0, $int10);
