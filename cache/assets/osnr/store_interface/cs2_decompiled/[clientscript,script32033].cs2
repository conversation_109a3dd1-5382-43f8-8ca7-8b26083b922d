// 32033
[clientscript,script32033](component $component0, component $component1, component $component2, component $component3, component $component4, component $layer5, enum $enum6)
sound_synth(interface_select1, 1, 0);
~xpdrops_setup_display_hoverdisable($component0);
if_sethide(false, $component2);
cc_deleteall($component1);
def_int $int7 = 24;
def_int $int8 = 0;
def_string $text0 = enum(int, string, $enum6, $int8);
def_int $count9 = 0;
def_int $count10 = enum_getoutputcount($enum6);
while ($count9 < $count10) {
	cc_create($component1, ^iftype_text, $int8);
	cc_setsize(0, $int7, ^setsize_minus, ^setsize_abs);
	cc_setposition(0, calc($count9 * $int7), ^setpos_abs_centre, ^setpos_abs_top);
	cc_setcolour(0xff981f);
	cc_settextshadow(true);
	cc_settextfont(verdana_13pt_regular);
	cc_settextalign(^settextalign_centre, ^settextalign_centre, 0);
	cc_settext($text0);
	$int8 = calc($int8 + 1);
	cc_create($component1, ^iftype_rectangle, $int8);
	cc_setsize(0, $int7, ^setsize_minus, ^setsize_abs);
	cc_setposition(0, calc($count9 * $int7), ^setpos_abs_centre, ^setpos_abs_top);
	cc_setfill(true);
	cc_setcolour(^white);
	cc_settrans(255);
	cc_setop(1, "Select");
	cc_setopbase("<col=ff981f><$text0></col>");
	cc_setonmouserepeat("cc_settrans(event_com, cc_getid, 190, -1)");
	cc_setonmouseleave("cc_settrans(event_com, cc_getid, 255, -1)");
	$int8 = calc($int8 + 1);
	$count9 = calc($count9 + 1);
	$text0 = enum(int, string, $enum6, $count9);
}
if_sethide(false, $component1);
def_int $int11 = calc($count9 * $int7);
def_int $height12 = calc($int11 + 4);
def_int $int13 = if_getx($component0);
def_int $int14 = if_gety($component0);
def_int $y15 = 0;
def_component $layer16 = if_getlayer($component0);
while ($layer16 ! $layer5) {
	$int13, $int14 = calc($int13 + if_getx($layer16) - if_getscrollx($layer16)), calc($int14 + if_gety($layer16) - if_getscrolly($layer16));
	$layer16 = if_getlayer($layer16);
}
$y15 = calc(if_gety($component0) + 11);
if_setposition(18, $y15, ^setpos_abs_left, ^setpos_abs_top, $component3);
if_setsize(if_getwidth($component0), $height12, ^setsize_abs, ^setsize_abs, $component3);
if_setscrollpos(0, 0, $component1);
if_setscrollsize(0, 0, $component1);
cc_deleteall($component4);
~scrollbar_vertical($component4, $component1, 792, 789, 790, 791, 773, 788);
if_setonclick("script6244($component2, $component0, $component1)", $component2);
