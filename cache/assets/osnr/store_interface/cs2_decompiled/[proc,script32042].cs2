// 32042
[proc,script32042](component $component0, string $string0, component $component1, component $component2, component $component3, component $component4, component $layer5)
cc_deleteall($component0);
def_enum $enum6 = enum_11014;
def_int $int7 = 0;
cc_create($component0, ^iftype_graphic, $int7);
$int7 = calc($int7 + 1);
cc_setsize(0, 20, ^setsize_minus, ^setsize_abs);
cc_setposition(0, 0, ^setpos_abs_centre, ^setpos_abs_bottom);
cc_setgraphic("tradebacking");
cc_settiling(true);
cc_setonclick("script32033($component0, $component1, $component2, $component3, $component4, $layer5, $enum6)");
cc_create($component0, ^iftype_rectangle, $int7);
$int7 = calc($int7 + 1);
cc_setsize(0, 20, ^setsize_minus, ^setsize_abs);
cc_setposition(0, 0, ^setpos_abs_centre, ^setpos_abs_bottom);
cc_setfill(false);
cc_setcolour(0x0e0e0c);
cc_create($component0, ^iftype_rectangle, $int7);
$int7 = calc($int7 + 1);
cc_setsize(2, 18, ^setsize_minus, ^setsize_abs);
cc_setposition(0, 1, ^setpos_abs_centre, ^setpos_abs_bottom);
cc_setfill(false);
cc_setcolour(0x474745);
cc_create($component0, ^iftype_graphic, $int7);
$int7 = calc($int7 + 1);
cc_setsize(16, 16, ^setsize_abs, ^setsize_abs);
cc_setposition(2, 2, ^setpos_abs_right, ^setpos_abs_bottom);
cc_create($component0, ^iftype_text, $int7);
$int7 = calc($int7 + 1);
cc_setsize(20, 16, ^setsize_minus, ^setsize_abs);
cc_setposition(2, 2, ^setpos_abs_left, ^setpos_abs_bottom);
cc_settextfont(p11_full);
cc_settextshadow(true);
cc_settextalign(^settextalign_centre, ^settextalign_centre, 0);
~script32028($component0, $enum6, %varbit17008);
cc_create($component0, ^iftype_text, $int7);
$int7 = calc($int7 + 1);
cc_setsize(0, 20, ^setsize_minus, ^setsize_minus);
cc_setposition(0, 0, ^setpos_abs_centre, ^setpos_abs_top);
cc_setcolour(0xff981f);
cc_settextshadow(true);
cc_settextfont(p12_full);
cc_settextalign(^settextalign_left, ^settextalign_centre, 0);
cc_settext("<$string0>:");
~xpdrops_setup_display_hoveroff($component0);
