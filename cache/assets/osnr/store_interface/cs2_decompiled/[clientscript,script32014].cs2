// 32014
[clientscript,script32014](component $component0)
cc_deleteall($component0);
def_int $int1 = 0;
cc_create($component0, ^iftype_graphic, $int1);
$int1 = calc($int1 + 1);
cc_setsize(0, 0, ^setsize_minus, ^setsize_minus);
cc_setposition(0, 0, ^setpos_abs_centre, ^setpos_abs_centre);
cc_setgraphic("tradebacking_dark");
cc_settiling(true);
cc_create($component0, ^iftype_rectangle, $int1);
$int1 = calc($int1 + 1);
cc_setsize(0, 0, ^setsize_minus, ^setsize_minus);
cc_setposition(0, 0, ^setpos_abs_centre, ^setpos_abs_centre);
cc_setcolour(^white);
cc_setfill(true);
cc_settrans(255);
~script4240($component0, cc_getid, 255, 225, 200);
$int1 = ~thinbox($component0, $int1);
cc_create($component0, ^iftype_text, $int1);
$int1 = calc($int1 + 1);
.cc_create($component0, ^iftype_graphic, $int1);
$int1 = calc($int1 + 1);
cc_setsize(0, 0, ^setsize_minus, ^setsize_minus);
.cc_setsize(13, 13, ^setsize_abs, ^setsize_abs);
cc_setcolour(0xffb83f);
cc_settextfont(b12_full);
cc_settextshadow(true);
cc_settextalign(^settextalign_centre, ^settextalign_centre, 0);
~script32015(%varbit17004);
