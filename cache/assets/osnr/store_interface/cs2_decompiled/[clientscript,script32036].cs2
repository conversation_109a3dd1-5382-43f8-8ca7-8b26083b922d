// 32036
[clientscript,script32036](component $component0, component $layer1, int $int2, component $component3, int $int4, component $component5, component $component6, component $component7, component $component8, component $component9, component $component10, component $component11, component $component12, component $component13, component $component14, component $component15, component $component16)
cc_deleteall($component0);
%varcint1110 = 0;
~steelborder($int2, "KaizenPVP Credit Packages", 0);
def_int $int17 = 0;
$int17 = ~v2_stone_button_filled($component3);
if_setonmouseover("v2_stone_button_change_in($component3, 0)", $component3);
if_setonmouseleave("v2_stone_button_change_out($component3, 0)", $component3);
if_setop(1, "Go Back", $component3);
if_setopbase("<col=ff981f>Store</col>", $component3);
def_string $text0 = "Go Back";
cc_create($component3, ^iftype_text, $int17);
$int17 = calc($int17 + 1);
cc_setposition(0, 0, ^setpos_abs_left, ^setpos_abs_top);
cc_setsize(0, 24, ^setsize_minus, ^setsize_abs);
cc_settextfont(verdana_11pt_regular);
cc_settextshadow(true);
cc_setcolour(0xff981f);
cc_settextalign(^settextalign_centre, ^settextalign_centre, 12);
cc_settext($text0);
~script32078($component10, $component11, $component12, $component13, $component14, $component15, $component16);
~script32042($component5, "Payment Options", $component6, $component7, $component8, $component9, $layer1);
