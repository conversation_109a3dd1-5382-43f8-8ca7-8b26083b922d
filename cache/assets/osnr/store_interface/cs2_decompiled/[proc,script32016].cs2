// 32016
[proc,script32016](int $int0, int $int1, component $component2, component $component3, int $int4, component $component5, int $int6, int $int7, int $int8, int $int9, string $op0)(int, int)
cc_create($component2, ^iftype_graphic, $int0);
$int0 = calc($int0 + 1);
cc_setsize(0, 25, ^setsize_minus, ^setsize_abs);
cc_setposition(0, $int1, ^setpos_abs_centre, ^setpos_abs_top);
cc_setgraphic("tradebacking");
cc_settiling(true);
cc_create($component2, ^iftype_text, $int0);
$int0 = calc($int0 + 1);
cc_setsize(0, 25, ^setsize_minus, ^setsize_abs);
cc_setposition(0, $int1, ^setpos_abs_centre, ^setpos_abs_top);
cc_setcolour(0xff981f);
cc_settextshadow(true);
cc_settextfont(b12_full);
cc_settextalign(^settextalign_centre, ^settextalign_centre, 0);
cc_settext($op0);
.cc_create($component3, ^iftype_rectangle, $int4);
$int4 = calc($int4 + 1);
.cc_setsize(0, $int7, ^setsize_minus, ^setsize_abs);
.cc_setposition(0, calc($int6 * $int7), ^setpos_abs_centre, ^setpos_abs_top);
.cc_setcolour(^white);
.cc_setfill(true);
if (calc($int6 % 2) = 0) {
	.cc_settrans(230);
	.cc_setonmouseleave("cc_settrans(event_com, event_comsubid, 230, -1)");
} else {
	.cc_settrans(215);
	.cc_setonmouseleave("cc_settrans(event_com, event_comsubid, 215, -1)");
}
if (~on_mobile = 0) {
	.cc_setonmouserepeat("cc_settrans(event_com, event_comsubid, 200, -1)");
}
.cc_setop(1, $op0);
.cc_setonop("clan_permissions_indexop($component2, $component5, cc_getid, $int1, 16711680, 16750623)");
.cc_create($component3, ^iftype_text, $int4);
$int4 = calc($int4 + 1);
.cc_setsize(0, $int7, ^setsize_minus, ^setsize_abs);
.cc_setposition(0, calc($int6 * $int7), ^setpos_abs_centre, ^setpos_abs_top);
if ($int8 = 1) {
	.cc_setcolour(^white);
} else {
	.cc_setcolour(0xff981f);
}
.cc_settextshadow(true);
if ($int8 = 1) {
	.cc_settextfont(p11_full);
} else {
	.cc_settextfont(p12_full);
}
.cc_settextalign(^settextalign_centre, ^settextalign_centre, 0);
if ($int8 = 1) {
	.cc_settext($op0);
} else {
	.cc_settext($op0);
}
return($int0, $int4);
