// 32040
[proc,script32040](component $component0)
if (%varcint1156 > clientclock) {
	def_int $int1 = calc((clientclock - %varcint1155) % 50);
	if ($int1 >= 25) {
		$int1 = calc(49 - $int1);
	}
	if_setcolour(~rgb_to_hex(255, calc(152 + interpolate(0, $int1, 0, 24, 103)), calc(31 + interpolate(0, $int1, 0, 24, 224))), $component0);
} else {
	if_setcolour(0xff981f, $component0);
	if_setontimer(null, $component0);
}
