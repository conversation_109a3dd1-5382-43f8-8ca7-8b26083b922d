// 32023
[proc,script32023](int $int0, component $component1, int $int2, int $int3, int $int4, int $colour5, string $text0, fontmetrics $fontmetrics6, int $colour7, int $int8, int $int9, int $int10)(int)
def_int $int11 = 4;
def_int $int12 = 1;
def_int $int13 = calc($int0 * ($int3 + $int4));
def_int $int14 = 0;
if (~script5808($int0, %varbit17006) = 1) {
	$int14 = 1;
}
def_int $int15 = 3712;
if ($int14 = 0) {
	$int15 = 3714;
}
def_int $int16 = calc($int11 - $int12);
def_int $comsubid17 = $int2;
cc_create($component1, ^iftype_rectangle, $comsubid17);
cc_setposition(1, calc($int13 + 1), ^setpos_abs_left, ^setpos_abs_top);
cc_setsize($int16, calc($int3 - 2), ^setsize_minus, ^setsize_abs);
cc_setfill(true);
cc_setcolour($colour5);
if ($int14 = 0) {
	$int16 = calc($int11 * 2 - $int12);
} else {
	$int16 = $int11;
}
if ($int14 = 0) {
	~create_graphic($component1, calc($int2 + 1), 897, $int11, calc($int13 + 2), 0, 0, $int16, calc($int3 - 4), 1, 0);
	cc_setop(1, "View");
	cc_setopbase("<col=ff981f><$text0></col>");
	cc_setonmouseover("cc_graphic_swapper($component1, event_comsubid, "tradebacking", -1)");
	cc_setonmouseleave("cc_graphic_swapper($component1, event_comsubid, "tradebacking_dark", -1)");
} else {
	~create_graphic($component1, calc($int2 + 1), 297, $int11, calc($int13 + 2), 0, 0, $int16, calc($int3 - 4), 1, 0);
}
if ($int13 = 0 & $int14 = 1) {
	$int16 = $int11;
} else {
	$int16 = calc(2 * $int11 - $int12);
}
~create_graphic($component1, calc($int2 + 2), 3528, $int11, $int13, 0, 0, $int16, $int11, 1, 0);
if (calc($int13 + $int3) = if_getheight($component1) & $int14 = 1) {
	$int16 = $int11;
} else {
	$int16 = calc(2 * $int11 - $int12);
}
~create_graphic($component1, calc($int2 + 3), 3529, $int11, calc($int13 + $int3 - $int11), 0, 0, $int16, $int11, 1, 0);
~create_graphic($component1, calc($int2 + 4), 3530, 0, calc($int13 + $int11), 0, 0, $int11, calc($int3 - 2 * $int11), 0, 0);
~create_graphic($component1, calc($int2 + 5), 3524, 0, $int13, 0, 0, $int11, $int11, 0, 0);
~create_graphic($component1, calc($int2 + 6), 3526, 0, calc($int13 + $int3 - $int11), 0, 0, $int11, $int11, 0, 0);
~create_graphic($component1, calc($int2 + 7), $int8, 3, calc($int13 + $int3 / 2 - $int10 / 2), 0, 0, $int9, $int10, 0, 0);
cc_create($component1, ^iftype_text, calc($int2 + 8));
def_int $int18 = 0;
if ($fontmetrics6 = null) {
	cc_sethide(true);
} else {
	$int18 = calc(8 + $int9);
	cc_setposition($int18, $int13, ^setpos_abs_left, ^setpos_abs_top);
	cc_setsize(calc($int18 + 6), $int3, ^setsize_minus, ^setsize_abs);
	cc_settextalign(^settextalign_left, ^settextalign_centre, 0);
	cc_settextshadow(true);
	cc_settextfont($fontmetrics6);
	cc_setcolour($colour7);
	cc_settext($text0);
}
if (cc_find($component1, $comsubid17) = ^true) {
	return(calc($int2 + 9));
}
return(0);
