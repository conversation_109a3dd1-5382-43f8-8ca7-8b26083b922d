package com.zenyte.plugins.item;

import com.near_reality.api.service.user.UserPlayerAttributesKt;
import com.zenyte.game.item.Item;
import com.zenyte.game.model.item.pluginextensions.ItemPlugin;
import com.zenyte.game.world.entity.player.Player;
import com.zenyte.game.util.Colour;
import com.zenyte.game.item.ItemId;
import com.near_reality.game.item.CustomItemId;

/**
 * Handles Donator Scrolls for adding donation total to a player.
 *
 * <AUTHOR> (Discord: imslickk)
 */
public class DonatorScroll extends ItemPlugin {

    @Override
    public void handle() {
        bind("Redeem", (player, item, slotId) -> {
            double amount = getScrollAmount(item.getId());
            if (amount <= 0)
                return;
            player.getInventory().deleteItem(item);
            double current = UserPlayerAttributesKt.getStoreTotalSpent(player);
            double newTotal = current + amount;
            player.getRankForDonationAmount(newTotal); //Fix this, can't check for errors
            player.getPacketDispatcher().sendMessage(new Item(ItemId.COINS_6964), Colour.DARK_BLUE.wrap("$" + amount) + " has been added to your total spent amount, making for a total of " + Colour.DARK_BLUE.wrap("$" + newTotal) + ".");
        });
    }

    private double getScrollAmount(int id) {
        return switch (id) {
            case CustomItemId.DONATOR_BOND_10 -> 10;
            case CustomItemId.DONATOR_BOND_25 -> 25;
            case CustomItemId.DONATOR_BOND_50 -> 50;
            case CustomItemId.DONATOR_BOND_100 -> 100;
            default -> 0;
        };
    }

    @Override
    public int[] getItems() {
        return new int[]{ CustomItemId.DONATOR_BOND_10, CustomItemId.DONATOR_BOND_25, CustomItemId.DONATOR_BOND_50, CustomItemId.DONATOR_BOND_100 };
    }
}
