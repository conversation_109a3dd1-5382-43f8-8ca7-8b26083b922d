package com.zenyte.plugins.itemonobject;

import com.zenyte.game.item.Item;
import com.zenyte.game.model.item.ItemOnObjectAction;
import com.zenyte.game.world.entity.Location;
import com.zenyte.game.world.entity.player.Player;
import com.zenyte.game.world.object.ObjectAction;
import com.zenyte.game.world.object.WorldObject;

/**
 * <AUTHOR> (Discord: imslickk)
 * Handles interactions with the Evil Chicken Lair
 * - Using raw chicken on shrine teleports to Evil Chicken's Lair
 * - Using portal teleports back to shrine area
 */
public class ChickenOnShrineAction implements ItemOnObjectAction, ObjectAction {

    private static final int RAW_CHICKEN = 2138;

    private static final int SHRINE = 12093;
    private static final int PORTAL = 12260;

    private static final Location SHRINE_LOCATION = new Location(2453, 4477, 0);
    private static final Location PORTAL_LOCATION = new Location(2459, 4354, 0);
    private static final Location EVIL_CHICKEN_LAIR = new Location(2461, 4356, 0);
    private static final Location SHRINE_RETURN = new Location(2453, 4476, 0);

    @Override
    public void handleItemOnObjectAction(Player player, Item item, int slot, WorldObject object) {
        if (item.getId() == RAW_CHICKEN && object.getId() == SHRINE) {
            if (object.getX() == SHRINE_LOCATION.getX() && object.getY() == SHRINE_LOCATION.getY() && object.getPlane() == SHRINE_LOCATION.getPlane()) {
                player.getInventory().deleteItem(item);
                player.teleport(EVIL_CHICKEN_LAIR);
                player.sendMessage("You offer the raw chicken to the shrine and are transported to the Evil Chicken's Lair!");
            }
        }
    }

    @Override
    public void handleObjectAction(Player player, WorldObject object, String name, int optionId, String option) {
        if (object.getId() == PORTAL) {
            if (object.getX() == PORTAL_LOCATION.getX() && object.getY() == PORTAL_LOCATION.getY() && object.getPlane() == PORTAL_LOCATION.getPlane()) {
                player.teleport(SHRINE_RETURN);
                player.sendMessage("You step through the portal and return to the shrine area.");
            }
        }
    }

    @Override
    public Object[] getItems() {
        return new Object[] { RAW_CHICKEN };
    }

    @Override
    public Object[] getObjects() {
        return new Object[] { SHRINE, PORTAL };
    }
}
