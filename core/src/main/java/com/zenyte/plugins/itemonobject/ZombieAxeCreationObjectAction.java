package com.zenyte.plugins.itemonobject;

import com.zenyte.game.content.skills.smithing.ZombieAxeCreationAction;
import com.zenyte.game.item.Item;
import com.zenyte.game.model.item.ItemOnObjectAction;
import com.zenyte.game.world.entity.player.Player;
import com.zenyte.game.world.object.WorldObject;

/**
 * 
 * <AUTHOR> (Discord: astra4)
 */
public class ZombieAxeCreationObjectAction implements ItemOnObjectAction {

	@Override
	public void handleItemOnObjectAction(Player player, Item item, int slot, WorldObject object) {
		player.getActionManager().setAction(new ZombieAxeCreationAction());
	}

	@Override
	public Object[] getItems() {
		return new Object[] { 28813 };
	}

	@Override
	public Object[] getObjects() {
		return new Object[] { "Anvil" };
	}

}
