package com.zenyte.game.content.itemupgrade;

import com.near_reality.game.item.CustomItemId;
import com.zenyte.game.item.Item;
import com.zenyte.game.item.ItemId;

enum UpgradeableItems {

        //W1(UpgradeCategory.WEAPON, ItemId.DARK_BOW_BH, 100, new Item[]{ new Item(11235,1), new Item(13307, 10000)}),
        //W4(UpgradeCategory.WEAPON, CustomItemId.RED_TWISTED_BOW, 100, new Item[]{ new Item(ItemId.TWISTED_BOW,1), new Item(CustomItemId.RED_TWISTED_BOW_PAINT, 1)}),
        //W5(UpgradeCategory.WEAPON, ItemId.DRAGON_CLAWS_OR, 100, new Item[]{ new Item(ItemId.DRAGON_CLAWS,1)}),
        //W10(UpgradeCategory.WEAPON, ItemId.DINHS_BLAZING_BULWARK, 100, new Item[]{new Item(ItemId.DINHS_BULWARK)}),

        // Weapons
        IBANS_STAFF_U(UpgradeCategory.WEAPON, ItemId.IBANS_STAFF_U, 100, new Item[]{new Item(ItemId.IBANS_STAFF)}, new Item(995, 1000000)),
        ARMADYL_BOWFA(UpgradeCategory.WEAPON, CustomItemId.ARMADYL_BOW, 100, new Item[]{new Item(ItemId.BOW_OF_FAERDHINEN_C_25884), new Item(CustomItemId.ARMADYL_SOUL_CRYSTAL, 1)}),
        ZAMORAK_BOWFA(UpgradeCategory.WEAPON, CustomItemId.ZAMORAK_BOW, 100, new Item[]{new Item(ItemId.BOW_OF_FAERDHINEN_C), new Item(CustomItemId.ZAMORAK_SOUL_CRYSTAL, 1)}),
        BANDOS_BOWFA(UpgradeCategory.WEAPON, CustomItemId.BANDOS_BOW, 100, new Item[]{new Item(ItemId.BOW_OF_FAERDHINEN_C_25886), new Item(CustomItemId.BANDOS_SOUL_CRYSTAL, 1)}),
        SARADOMIN_BOWFA(UpgradeCategory.WEAPON, CustomItemId.SARADOMIN_BOW, 100, new Item[]{new Item(ItemId.BOW_OF_FAERDHINEN_C_25896), new Item(CustomItemId.SARADOMIN_SOUL_CRYSTAL, 1)}),
        HOLY_GREAT_WARHAMMER(UpgradeCategory.WEAPON, CustomItemId.HOLY_GREAT_WARHAMMER, 100, new Item[]{new Item(ItemId.DRAGON_WARHAMMER), new Item(ItemId.ANGELIC_ARTIFACT, 1)}),
        HOLY_GREAT_LANCE(UpgradeCategory.WEAPON, CustomItemId.HOLY_GREAT_LANCE, 100, new Item[]{new Item(ItemId.DRAGON_HUNTER_LANCE), new Item(ItemId.ANGELIC_ARTIFACT, 1)}),

        // Armour
        HELM_OF_NEITIZNOT_OR(UpgradeCategory.ARMOUR, ItemId.HELM_OF_NEITIZNOT_OR, 50, new Item[]{new Item(ItemId.HELM_OF_NEITIZNOT)}, new Item(ItemId.IMBUE_SCROLL, 1)),
        FIGHTER_TORSO_OR(UpgradeCategory.ARMOUR, ItemId.FIGHTER_TORSO_OR, 50, new Item[]{new Item(ItemId.FIGHTER_TORSO)}, new Item(ItemId.IMBUE_SCROLL, 1)),
        BANDOS_CHESTPLATE_OR(UpgradeCategory.ARMOUR, 32142, 50, new Item[]{new Item(ItemId.BANDOS_CHESTPLATE,1)}, new Item(ItemId.BANDOSIAN_COMPONENTS, 1)),
        //BANDOS_CHESTPLATE_OR_2(UpgradeCategory.ARMOUR, ItemId.BANDOS_CHESTPLATE_OR, 50, new Item[]{new Item(ItemId.BANDOS_CHESTPLATE,1)}, new Item(ItemId.BANDOSIAN_COMPONENTS, 1)),
        BANDOS_TASSETS_OR(UpgradeCategory.ARMOUR, 32144, 100, new Item[]{new Item(ItemId.BANDOS_TASSETS,1)}, new Item(ItemId.BANDOSIAN_COMPONENTS, 1)),
        //BANDOS_TASSETS_OR_2(UpgradeCategory.ARMOUR, ItemId.BANDOS_TASSETS_OR, 50, new Item[]{new Item(ItemId.BANDOS_TASSETS,1)}, new Item(ItemId.BANDOSIAN_COMPONENTS, 1)),

        // Jewellery
        SALVE_AMULETEI(UpgradeCategory.JEWELLERY, ItemId.SALVE_AMULETEI, 50, new Item[]{new Item(ItemId.SALVE_AMULETI)}),

        // Misc


        ;

        public transient final UpgradeCategory category;
        public final int id;
        public final int chance;

        public final Item[] required;
        public final Item ingredient;

        UpgradeableItems(UpgradeCategory category, int id, int chance, Item[] required, Item ingredient) {
            this.category = category;
            this.id = id;
            this.chance = chance;
            this.required = required;
            this.ingredient = ingredient;
        }

    UpgradeableItems(UpgradeCategory category, int id, int chance, Item[] required) {
        this.category = category;
        this.id = id;
        this.chance = chance;
        this.required = required;
        this.ingredient = new Item(995, 5000000);
    }
    }