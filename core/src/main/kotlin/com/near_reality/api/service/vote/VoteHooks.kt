package com.near_reality.api.service.vote

import com.google.common.eventbus.Subscribe
import com.near_reality.api.APIClient
import com.zenyte.game.GameConstants
import com.zenyte.game.item.Item
import com.zenyte.game.util.Colour
import com.zenyte.game.world.entity.player.dialogue.dialogue
import com.zenyte.plugins.events.LoginEvent


/**
 * Represents a collection of hooks that are related to voting.
 *
 * <AUTHOR>
 */
object VoteHooks {

    /**
     * Registers a login event hook that will remind the player to vote if they haven't done so today.
     */
    @Subscribe
    @JvmStatic
    fun onLogin(event: LoginEvent) {
        val player = event.player
        if (player.getBooleanAttribute("registered")
            && System.currentTimeMillis() >= player.lastVoteClaimTime && APIClient.enabled && VoteAPIService.enabled) {
            player.dialogue {
                item(Item(32148), "${Colour.RS_RED.wrap("Vote for a Reward!")}<br>" +
                        "You have not voted today, vote now for ${Colour.DARK_BLUE.wrap("Vote Points")}, " +
                        "a chance for free ${Colour.DARK_BLUE.wrap("Clue Caskets")}, ${Colour.DARK_BLUE.wrap("GP")}, " +
                        "and to help spawn the ${Colour.RS_RED.wrap("Vote Boss, Galvek")}!")
            }
        }
    }
}
