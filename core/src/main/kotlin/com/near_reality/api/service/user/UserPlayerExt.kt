package com.near_reality.api.service.user

import com.near_reality.api.service.vote.totalVotePoints
import com.zenyte.game.GameInterface
import com.zenyte.game.model.ui.Interface
import com.zenyte.game.util.Colour
import com.zenyte.game.world.entity.player.Player


/**
 * Updates the player info tab with their current member rank and total donated amount.
 */
internal fun Player.updateDonationInfoOnPlayerDetailsTab() {
    GameInterface.DASHBOARD.plugin.ifPresent { plugin: Interface ->
        packetDispatcher.run {
//            sendComponentText(
//                GameInterface.GAME_NOTICEBOARD,
//                plugin.getComponent("Member Rank"),
//                buildString {
//                    append("Member: ")
//                    append(memberCrown.crownTag)
//                    append(Colour.WHITE.wrap(memberName))
//                }
//            )
            sendComponentText(
                GameInterface.DASHBOARD,
                plugin.getComponent("Total Donated"),
                buildString {
                    append("Total Donated: ")
                    append(Colour.WHITE.wrap("$${storeTotalSpent}"))
                }
            )
        }
    }
}

/**
 * Updates the player info tab with their current vote points.
 */
fun Player.updateVoteStatisticOnPlayerDetailsTab() {
    GameInterface.DASHBOARD.plugin.ifPresent { plugin: Interface ->
        packetDispatcher.sendComponentText(
            GameInterface.DASHBOARD,
            plugin.getComponent("Vote Points"),
            buildString {
                append("Vote Points: ")
                append(Colour.WHITE.wrap(totalVotePoints))
            }
        )
    }
}
