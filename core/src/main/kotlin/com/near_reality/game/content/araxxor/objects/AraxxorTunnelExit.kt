package com.near_reality.game.content.araxxor.objects

import com.near_reality.game.content.araxxor.AraxxorInstance
import com.zenyte.game.task.WorldTasksManager.schedule
import com.zenyte.game.util.Direction
import com.zenyte.game.world.entity.Location
import com.zenyte.game.world.entity.masks.Animation
import com.zenyte.game.world.entity.player.Player
import com.zenyte.game.world.entity.player.cutscene.FadeScreen
import com.zenyte.game.world.`object`.ObjectAction
import com.zenyte.game.world.`object`.WorldObject
import com.zenyte.plugins.dialogue.PlainChat

/**
 * <AUTHOR> | Glabay-Studios
 * @project near-reality-server
 * @social Discord: Glabay
 * @since 2024-10-23
 */
class AraxxorTunnelExit : ObjectAction {
    override fun handleObjectAction(player: Player?, `object`: WorldObject?, name: String?, optionId: Int, option: String?) {
        player ?: return; `object` ?: return

        val exitLocation = Location(3657, 3407, 0)
        player.lock()
        val screen = FadeScreen(player) {
            AraxxorInstance(player).destroyRegion()
            player.setLocation(exitLocation)
            player.faceDirection(Direction.EAST)
            player.dialogueManager.start(PlainChat(player, "You escape through the webbed tunnel."))
        }
        player.dialogueManager.start(PlainChat(player, "You escape through the webbed tunnel.", false))
        screen.fade()
        player.animation = Animation.CRAWL
        schedule(2) {
            screen.unfade()
            player.unlock()
        }

    }

    override fun getObjects(): Array<Any> = arrayOf(54274)
}