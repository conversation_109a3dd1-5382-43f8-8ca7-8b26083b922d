package com.zenyte.logger

import org.slf4j.LoggerFactory
import java.io.PrintStream

/**
 * <AUTHOR> | Glabay-Studios
 * @project near-reality-server
 * @social Discord: Glabay
 * @since 2025-02-14
 */
object ExilesPrintStream: PrintStream(System.err) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    @JvmStatic
    fun getErrorStream(): PrintStream {
        // TODO: evaluate the error coming through

        return PrintStream(System.err)
    }

    fun handleStackTrace(throwable: Throwable) {
        logger.error(throwable.message, throwable)
    }
}
